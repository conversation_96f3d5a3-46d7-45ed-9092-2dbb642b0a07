import { Metadata } from 'next';
import { Locale } from '../../../../lib/i18n';
import { generateSubcategoryMetadata } from '../../../../components/SEO/SubcategorySEO';

interface SubcategoryLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string; subcategoryId: string }>;
}

// دالة لجلب بيانات الفئة الفرعية للـ SEO
async function getSubcategoryForSEO(subcategoryId: string) {
  try {
    const baseUrl = process.env.NODE_ENV === 'development' 
      ? 'http://localhost:3000' 
      : (process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com');
      
    const response = await fetch(`${baseUrl}/api/subcategories?id=${subcategoryId}`, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      console.log(`Subcategory API returned ${response.status} for ID: ${subcategoryId}`);
      return null;
    }
    
    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error('Error fetching subcategory for SEO:', error);
    // إرجاع بيانات افتراضية بدلاً من null
    return {
      id: subcategoryId,
      name: 'فئة فرعية',
      name_ar: 'فئة فرعية',
      description: 'وصف الفئة الفرعية',
      description_ar: 'وصف الفئة الفرعية',
    };
  }
}

// دالة لجلب بيانات الفئة الرئيسية
async function getCategoryForSEO(categoryId: string) {
  try {
    const baseUrl = process.env.NODE_ENV === 'development' 
      ? 'http://localhost:3000' 
      : (process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com');
      
    const response = await fetch(`${baseUrl}/api/categories?id=${categoryId}`, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      return null;
    }
    
    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error('Error fetching category for SEO:', error);
    return null;
  }
}

// إنشاء metadata للفئة الفرعية
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string; subcategoryId: string }>
}): Promise<Metadata> {
  const { locale: localeParam, subcategoryId } = await params;
  const locale = (localeParam || 'ar') as Locale;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  
  // جلب بيانات الفئة الفرعية
  const subcategory = await getSubcategoryForSEO(subcategoryId);
  
  if (!subcategory) {
    return {
      title: locale === 'ar' ? 'فئة فرعية غير موجودة | دروب هجر' : 'Subcategory Not Found | DROOB HAJER',
      description: locale === 'ar' 
        ? 'عذراً، لم نتمكن من العثور على هذه الفئة الفرعية'
        : 'Sorry, we could not find this subcategory',
    };
  }
  
  // جلب بيانات الفئة الرئيسية إذا كانت متوفرة
  let category = null;
  if (subcategory.category_id) {
    category = await getCategoryForSEO(subcategory.category_id);
  }
  
  // استخدام دالة SEO المحسنة الجديدة
  return generateSubcategoryMetadata(subcategory, category, locale);
}

export default async function SubcategoryLayout({ 
  children 
}: SubcategoryLayoutProps) {
  return (
    <>
      {children}
    </>
  );
}

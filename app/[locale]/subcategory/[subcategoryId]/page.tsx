'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Locale } from '../../../../lib/i18n';
import { getTranslation, TranslationKey, NestedTranslationKey } from '../../../../lib/translations';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';
import ProductCard from '../../../../components/ProductCard';
import WhatsAppButton from '../../../../components/WhatsAppButton';
import { SubcategoryJsonLd } from '../../../../components/SEO/SubcategorySEO';
import { ProductWithDetails, Category, Subcategory } from '../../../../types/mysql-database';

export default function SubcategoryPage() {
  const params = useParams();
  const router = useRouter();
  const locale = (params?.locale || 'ar') as Locale;
  const subcategoryId = params?.subcategoryId as string;
  
  const [products, setProducts] = useState<ProductWithDetails[]>([]);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [availabilityFilter, setAvailabilityFilter] = useState('all');

  const t = (key: TranslationKey | NestedTranslationKey) => getTranslation(locale, key);

  const fetchSubcategoryData = useCallback(async () => {
    try {
      setLoading(true);

      // جلب بيانات الفئة الفرعية
      const subcategoriesResponse = await fetch(`/api/subcategories?id=${subcategoryId}`);
      if (subcategoriesResponse.ok) {
        const subcategoriesResult = await subcategoriesResponse.json();
        console.log('📦 استجابة API الفئة الفرعية:', subcategoriesResult);

        if (subcategoriesResult.success && subcategoriesResult.data) {
          const currentSubcategory = subcategoriesResult.data;
          setSubcategory(currentSubcategory);

          // جلب بيانات الفئة الرئيسية
          const categoriesResponse = await fetch(`/api/categories?id=${currentSubcategory.category_id}`);
          if (categoriesResponse.ok) {
            const categoriesResult = await categoriesResponse.json();
            console.log('📦 استجابة API الفئة الرئيسية:', categoriesResult);

            if (categoriesResult.success && categoriesResult.data) {
              setCategory(categoriesResult.data);
            }
          }
        } else {
          console.error('❌ فشل في جلب الفئة الفرعية:', subcategoriesResult);
          router.push(`/${locale}/products`);
          return;
        }
      } else {
        console.error('❌ خطأ في استجابة API الفئة الفرعية');
        router.push(`/${locale}/products`);
        return;
      }

      // جلب المنتجات الخاصة بالفئة الفرعية
      const productsResponse = await fetch(`/api/products?subcategoryId=${subcategoryId}`);
      if (productsResponse.ok) {
        const productsResult = await productsResponse.json();
        console.log('📦 استجابة API المنتجات:', productsResult);

        if (productsResult.success && productsResult.data) {
          setProducts(productsResult.data);
        } else {
          console.error('❌ فشل في جلب المنتجات:', productsResult);
          setProducts([]);
        }
      } else {
        console.error('❌ خطأ في استجابة API المنتجات');
        setProducts([]);
      }

    } catch (error) {
      console.error('❌ خطأ في جلب بيانات الفئة الفرعية:', error);
      setSubcategory(null);
      setCategory(null);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [subcategoryId, locale, router]);

  useEffect(() => {
    fetchSubcategoryData();
  }, [fetchSubcategoryData]);

  const filteredProducts = products.filter(product => {
    // فلتر البحث
    const searchField = locale === 'ar' ? product.title_ar : product.title;
    const matchesSearch = searchField.toLowerCase().includes(searchTerm.toLowerCase());
    
    // فلتر التوفر
    const matchesAvailability = availabilityFilter === 'all' || 
      (availabilityFilter === 'available' && product.is_available) ||
      (availabilityFilter === 'unavailable' && !product.is_available);
    
    return matchesSearch && matchesAvailability && product.is_active;
  });

  // ترتيب المنتجات
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        const nameA = locale === 'ar' ? a.title_ar : a.title;
        const nameB = locale === 'ar' ? b.title_ar : b.title;
        return nameA.localeCompare(nameB);
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'newest':
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
              <p className="text-gray-700 text-lg font-medium">
                {locale === 'ar' ? 'جاري تحميل منتجات الفئة الفرعية...' : 'Loading subcategory products...'}
              </p>
            </div>
          </div>
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </>
    );
  }

  if (!subcategory) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <i className="ri-error-warning-line text-6xl text-red-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {locale === 'ar' ? 'الفئة الفرعية غير موجودة' : 'Subcategory not found'}
              </h3>
              <p className="text-gray-600 mb-6">
                {locale === 'ar' 
                  ? 'الفئة الفرعية التي تبحث عنها غير موجودة أو تم حذفها'
                  : 'The subcategory you are looking for does not exist or has been deleted'
                }
              </p>
              <button
                onClick={() => router.push(`/${locale}/products`)}
                className="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300"
              >
                {locale === 'ar' ? 'العودة للمنتجات' : 'Back to Products'}
              </button>
            </div>
          </div>
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </>
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main className="min-h-screen bg-gray-50">
        {/* Page Header */}
        <section className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-700 py-16">
          <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-white/80 mb-6">
              <a href={`/${locale}`} className="hover:text-white transition-colors">
                {t('home')}
              </a>
              <span>/</span>
              {category && (
                <>
                  <a 
                    href={`/${locale}/category/${category.id}`} 
                    className="hover:text-white transition-colors"
                  >
                    {locale === 'ar' ? category.name_ar : category.name}
                  </a>
                  <span>/</span>
                </>
              )}
              <span className="text-white font-medium">
                {locale === 'ar' ? subcategory.name_ar : subcategory.name}
              </span>
            </nav>
            
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {locale === 'ar' ? subcategory.name_ar : subcategory.name}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto mb-6">
                {locale === 'ar' ? subcategory.description_ar : subcategory.description}
              </p>
              <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse text-white/80">
                <span className="flex items-center space-x-2 rtl:space-x-reverse">
                  <i className="ri-shopping-bag-line"></i>
                  <span>{sortedProducts.length} {locale === 'ar' ? 'منتج' : 'Products'}</span>
                </span>
              </div>
            </div>
          </div>
        </section>

        {/* SEO Content Section */}
        <section className="py-8 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              {/* محتوى محسن للبورسلين */}
              {(subcategory?.name_ar?.includes('البورسلين') || subcategory?.name?.toLowerCase().includes('chinaware')) && (
                <div className="prose prose-lg max-w-none text-gray-700">
                  {locale === 'ar' ? (
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 mb-4">
                        أدوات البورسلين الفاخرة للفنادق والمطاعم الراقية
                      </h2>
                      <p className="mb-4">
                        تقدم دروب هجر مجموعة شاملة من <strong>أدوات البورسلين الفاخرة</strong> و<strong>معدات تقديم البورسلين</strong> المصممة خصيصاً للفنادق والمطاعم والمنتجعات الراقية.
                        نوفر <strong>أطباق بورسلين عالية الجودة</strong> و<strong>فناجين وصحون بورسلين</strong> و<strong>أطباق تقديم احترافية</strong> تجمع بين الأناقة والمتانة.
                      </p>
                      <p className="mb-4">
                        تشمل مجموعتنا من <strong>تجهيزات البورسلين الفندقية</strong>: أطباق العشاء، أطباق الحلى، فناجين الشاي والقهوة، صحون التقديم،
                        وأوعية الحساء المصنوعة من <strong>بورسلين مقاوم للحرارة</strong> وآمن للاستخدام في غسالة الصحون.
                        جميع منتجاتنا من <strong>أدوات المائدة البورسلين</strong> تتميز بتصاميم أنيقة تناسب جميع مناسبات الضيافة الفندقية.
                      </p>
                      <p>
                        اختر من مجموعة واسعة من <strong>معدات الضيافة البورسلين</strong> و<strong>أدوات تقديم الطعام الفاخرة</strong> التي تلبي احتياجات
                        المطابخ الفندقية والمطاعم الراقية. نقدم حلولاً متكاملة مع خدمة عملاء متخصصة وعروض أسعار مخصصة لمشاريعكم الفندقية.
                      </p>
                    </div>
                  ) : (
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 mb-4">
                        Luxury Chinaware for Hotels and Fine Dining Restaurants
                      </h2>
                      <p className="mb-4">
                        DROOB HAJER offers a comprehensive collection of <strong>luxury chinaware</strong> and <strong>porcelain serving equipment</strong> designed specifically for hotels, restaurants, and upscale resorts.
                        We provide <strong>high-quality porcelain plates</strong>, <strong>porcelain cups and saucers</strong>, and <strong>professional serving platters</strong> that combine elegance with durability.
                      </p>
                      <p className="mb-4">
                        Our <strong>hotel porcelain equipment</strong> collection includes: dinner plates, dessert plates, tea and coffee cups, serving dishes,
                        and soup bowls made from <strong>heat-resistant porcelain</strong> that is dishwasher-safe.
                        All our <strong>porcelain tableware</strong> features elegant designs suitable for all hotel hospitality occasions.
                      </p>
                      <p>
                        Choose from a wide range of <strong>porcelain hospitality equipment</strong> and <strong>luxury food serving tools</strong> that meet the needs of
                        hotel kitchens and fine dining restaurants. We provide complete solutions with specialized customer service and custom quotations for your hotel projects.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* محتوى عام للفئات الفرعية الأخرى */}
              {!(subcategory?.name_ar?.includes('البورسلين') || subcategory?.name?.toLowerCase().includes('chinaware')) && (
                <div className="prose prose-lg max-w-none text-gray-700">
                  {locale === 'ar' ? (
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 mb-4">
                        {subcategory?.name_ar} - تجهيزات فندقية احترافية
                      </h2>
                      <p className="mb-4">
                        اكتشف مجموعة <strong>{subcategory?.name_ar}</strong> المتميزة في دروب هجر، المتخصصة في توفير <strong>تجهيزات فندقية عالية الجودة</strong> و<strong>معدات ضيافة احترافية</strong>.
                        نقدم حلولاً شاملة تلبي احتياجات الفنادق والمطاعم والمنتجعات بأعلى معايير الجودة والكفاءة.
                      </p>
                      <p className="mb-4">
                        تشمل مجموعتنا من <strong>معدات {subcategory?.name_ar}</strong> منتجات مصممة خصيصاً للاستخدام الاحترافي في بيئات الضيافة المختلفة.
                        جميع منتجاتنا تتميز بالمتانة والأناقة، مما يجعلها الخيار الأمثل للمشاريع الفندقية الراقية.
                      </p>
                      <p>
                        احصل على <strong>عروض أسعار مخصصة</strong> لمشروعك الفندقي واستفد من خبرتنا الواسعة في مجال <strong>التجهيزات الفندقية</strong> و<strong>مستلزمات الضيافة</strong>.
                        فريقنا المتخصص جاهز لمساعدتك في اختيار الحلول المناسبة لاحتياجاتك.
                      </p>
                    </div>
                  ) : (
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 mb-4">
                        {subcategory?.name} - Professional Hotel Equipment
                      </h2>
                      <p className="mb-4">
                        Discover our premium <strong>{subcategory?.name}</strong> collection at DROOB HAJER, specializing in providing <strong>high-quality hotel equipment</strong> and <strong>professional hospitality supplies</strong>.
                        We offer comprehensive solutions that meet the needs of hotels, restaurants, and resorts with the highest standards of quality and efficiency.
                      </p>
                      <p className="mb-4">
                        Our <strong>{subcategory?.name} equipment</strong> collection includes products designed specifically for professional use in various hospitality environments.
                        All our products feature durability and elegance, making them the ideal choice for upscale hotel projects.
                      </p>
                      <p>
                        Get <strong>custom quotations</strong> for your hotel project and benefit from our extensive experience in <strong>hotel equipment</strong> and <strong>hospitality supplies</strong>.
                        Our specialized team is ready to help you choose the right solutions for your needs.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Products Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Sidebar Filters */}
              <div className="lg:w-1/4">
                {/* Search Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'البحث' : 'Search'}
                  </label>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder={locale === 'ar' ? 'ابحث في المنتجات...' : 'Search products...'}
                  />
                </div>

                {/* Availability Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'التوفر' : 'Availability'}
                  </label>
                  <select
                    value={availabilityFilter}
                    onChange={(e) => setAvailabilityFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="all">
                      {locale === 'ar' ? 'جميع المنتجات' : 'All Products'}
                    </option>
                    <option value="available">
                      {locale === 'ar' ? 'متاح فقط' : 'Available Only'}
                    </option>
                    <option value="unavailable">
                      {locale === 'ar' ? 'غير متاح' : 'Unavailable'}
                    </option>
                  </select>
                </div>

                {/* Sort Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'ترتيب حسب' : 'Sort by'}
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="name">
                      {locale === 'ar' ? 'الاسم' : 'Name'}
                    </option>
                    <option value="price-low">
                      {locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'}
                    </option>
                    <option value="price-high">
                      {locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'}
                    </option>
                    <option value="newest">
                      {locale === 'ar' ? 'الأحدث' : 'Newest'}
                    </option>
                  </select>
                </div>

                {/* Clear Filters */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setAvailabilityFilter('all');
                      setSortBy('name');
                    }}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                  >
                    {locale === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
                  </button>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">
                {/* Results Summary */}
                <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">
                      {locale === 'ar' ? 'عرض' : 'Showing'}
                      <span className="font-semibold text-blue-600 mx-1">{sortedProducts.length}</span>
                      {locale === 'ar' ? 'من أصل' : 'of'}
                      <span className="font-semibold text-blue-600 mx-1">{products.length}</span>
                      {locale === 'ar' ? 'منتج' : 'products'}
                    </span>
                  </div>
                </div>

                {/* Products Grid */}
                {sortedProducts.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {sortedProducts.map((product) => (
                      <ProductCard
                        key={product.id}
                        id={product.id}
                        image={product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
                        title={locale === 'ar' ? product.title_ar : product.title}
                        description={locale === 'ar' ? (product.description_ar || '') : (product.description || '')}
                        price={product.price}
                        available={product.is_available}
                        locale={locale}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-16 bg-white rounded-2xl shadow-lg">
                    <i className="ri-search-line text-6xl text-gray-400 mb-4"></i>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      {locale === 'ar' ? 'لا توجد منتجات' : 'No products found'}
                    </h3>
                    <p className="text-gray-600 mb-6">
                      {locale === 'ar'
                        ? 'لا توجد منتجات في هذه الفئة الفرعية حالياً'
                        : 'No products are currently available in this subcategory'
                      }
                    </p>
                  </div>
                )}

                {/* Back Navigation */}
                <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
                  {category && (
                    <button
                      onClick={() => router.push(`/${locale}/category/${category.id}`)}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse"
                    >
                      <i className="ri-arrow-left-line"></i>
                      <span>{locale === 'ar' ? 'العودة للفئة الرئيسية' : 'Back to Category'}</span>
                    </button>
                  )}
                  <button
                    onClick={() => router.push(`/${locale}/products`)}
                    className="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse"
                  >
                    <i className="ri-shopping-bag-line"></i>
                    <span>{locale === 'ar' ? 'جميع المنتجات' : 'All Products'}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />

      {/* JSON-LD للفئة الفرعية */}
      {subcategory && (
        <SubcategoryJsonLd
          subcategory={subcategory}
          category={category || undefined}
          locale={locale}
        />
      )}
    </>
  );
}
